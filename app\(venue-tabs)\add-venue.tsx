import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { useData } from '@/contexts/DataContext';
import { MapPin, DollarSign, Users, Clock, Plus, X } from 'lucide-react-native';

export default function AddVenue() {
  const { user } = useAuth();
  const { addVenue } = useData();
  const [loading, setLoading] = useState(false);
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    address: '',
    city: '',
    price_per_hour: '',
    capacity: '',
    images: ['https://images.pexels.com/photos/114296/pexels-photo-114296.jpeg'],
    amenities: [] as string[],
    available_hours: [] as string[],
  });
  
  const [newAmenity, setNewAmenity] = useState('');
  
  const defaultHours = [
    '08:00', '09:00', '10:00', '11:00', '12:00', '13:00',
    '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00', '21:00'
  ];

  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const addAmenity = () => {
    if (newAmenity.trim() && !formData.amenities.includes(newAmenity.trim())) {
      setFormData(prev => ({
        ...prev,
        amenities: [...prev.amenities, newAmenity.trim()]
      }));
      setNewAmenity('');
    }
  };

  const removeAmenity = (amenity: string) => {
    setFormData(prev => ({
      ...prev,
      amenities: prev.amenities.filter(a => a !== amenity)
    }));
  };

  const toggleHour = (hour: string) => {
    setFormData(prev => ({
      ...prev,
      available_hours: prev.available_hours.includes(hour)
        ? prev.available_hours.filter(h => h !== hour)
        : [...prev.available_hours, hour]
    }));
  };

  const handleSubmit = async () => {
    const { name, description, address, city, price_per_hour, capacity } = formData;
    
    if (!name || !description || !address || !city || !price_per_hour || !capacity) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    if (formData.amenities.length === 0) {
      Alert.alert('Error', 'Please add at least one amenity');
      return;
    }

    if (formData.available_hours.length === 0) {
      Alert.alert('Error', 'Please select available hours');
      return;
    }

    setLoading(true);
    
    try {
      addVenue({
        owner_id: user!.id,
        name,
        description,
        address,
        city,
        day_price_per_hour: parseFloat(price_per_hour),
        night_price_per_hour: parseFloat(price_per_hour) * 1.2, // 20% higher for night
        night_time_start: '18:00',
        opening_time: formData.available_hours[0] || '08:00',
        closing_time: formData.available_hours[formData.available_hours.length - 1] || '22:00',
        facilities: formData.amenities,
        status: 'pending',
        strikes: 0,
        rating: 0,
        total_reviews: 0,
      });
      
      Alert.alert(
        'Success',
        'Venue submitted successfully! It will be reviewed by admin before going live.',
        [{ text: 'OK', onPress: () => {
          // Reset form
          setFormData({
            name: '',
            description: '',
            address: '',
            city: '',
            price_per_hour: '',
            capacity: '',
            images: ['https://images.pexels.com/photos/114296/pexels-photo-114296.jpeg'],
            amenities: [],
            available_hours: [],
          });
        }}]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to submit venue. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Add New Venue</Text>
        <Text style={styles.subtitle}>Create a listing for your football venue</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.form}>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Basic Information</Text>
            
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Venue Name *</Text>
              <TextInput
                style={styles.input}
                placeholder="Enter venue name"
                value={formData.name}
                onChangeText={(value) => updateFormData('name', value)}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Description *</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                placeholder="Describe your venue..."
                value={formData.description}
                onChangeText={(value) => updateFormData('description', value)}
                multiline
                numberOfLines={4}
              />
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Location</Text>
            
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Address *</Text>
              <View style={styles.inputWithIcon}>
                <MapPin size={20} color="#6B7280" />
                <TextInput
                  style={styles.inputWithIconText}
                  placeholder="Enter full address"
                  value={formData.address}
                  onChangeText={(value) => updateFormData('address', value)}
                />
              </View>
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>City *</Text>
              <TextInput
                style={styles.input}
                placeholder="Enter city"
                value={formData.city}
                onChangeText={(value) => updateFormData('city', value)}
              />
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Pricing & Capacity</Text>
            
            <View style={styles.row}>
              <View style={[styles.inputContainer, { flex: 1, marginRight: 8 }]}>
                <Text style={styles.inputLabel}>Price per Hour *</Text>
                <View style={styles.inputWithIcon}>
                  <DollarSign size={20} color="#6B7280" />
                  <TextInput
                    style={styles.inputWithIconText}
                    placeholder="0"
                    value={formData.price_per_hour}
                    onChangeText={(value) => updateFormData('price_per_hour', value)}
                    keyboardType="numeric"
                  />
                </View>
              </View>

              <View style={[styles.inputContainer, { flex: 1, marginLeft: 8 }]}>
                <Text style={styles.inputLabel}>Capacity *</Text>
                <View style={styles.inputWithIcon}>
                  <Users size={20} color="#6B7280" />
                  <TextInput
                    style={styles.inputWithIconText}
                    placeholder="0"
                    value={formData.capacity}
                    onChangeText={(value) => updateFormData('capacity', value)}
                    keyboardType="numeric"
                  />
                </View>
              </View>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Amenities</Text>
            
            <View style={styles.amenityInput}>
              <TextInput
                style={styles.amenityInputField}
                placeholder="Add amenity (e.g., Floodlights, Parking)"
                value={newAmenity}
                onChangeText={setNewAmenity}
              />
              <TouchableOpacity style={styles.addButton} onPress={addAmenity}>
                <Plus size={20} color="#FFFFFF" />
              </TouchableOpacity>
            </View>

            <View style={styles.amenityList}>
              {formData.amenities.map((amenity, index) => (
                <View key={index} style={styles.amenityTag}>
                  <Text style={styles.amenityText}>{amenity}</Text>
                  <TouchableOpacity onPress={() => removeAmenity(amenity)}>
                    <X size={16} color="#6B7280" />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Available Hours</Text>
            <Text style={styles.sectionSubtitle}>Select the hours when your venue is available</Text>
            
            <View style={styles.hoursGrid}>
              {defaultHours.map((hour) => (
                <TouchableOpacity
                  key={hour}
                  style={[
                    styles.hourButton,
                    formData.available_hours.includes(hour) && styles.hourButtonActive
                  ]}
                  onPress={() => toggleHour(hour)}
                >
                  <Clock size={16} color={formData.available_hours.includes(hour) ? '#FFFFFF' : '#6B7280'} />
                  <Text style={[
                    styles.hourButtonText,
                    formData.available_hours.includes(hour) && styles.hourButtonTextActive
                  ]}>
                    {hour}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <TouchableOpacity
            style={[styles.submitButton, loading && styles.submitButtonDisabled]}
            onPress={handleSubmit}
            disabled={loading}
          >
            <Text style={styles.submitButtonText}>
              {loading ? 'Submitting...' : 'Submit Venue for Review'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  content: {
    flex: 1,
  },
  form: {
    padding: 24,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  inputWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 16,
  },
  inputWithIconText: {
    flex: 1,
    paddingVertical: 12,
    paddingLeft: 12,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
  },
  row: {
    flexDirection: 'row',
  },
  amenityInput: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  amenityInputField: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
    marginRight: 8,
  },
  addButton: {
    backgroundColor: '#3B82F6',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  amenityList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  amenityTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  amenityText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#111827',
    marginRight: 8,
  },
  hoursGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  hourButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  hourButtonActive: {
    backgroundColor: '#3B82F6',
    borderColor: '#3B82F6',
  },
  hourButtonText: {
    marginLeft: 4,
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  hourButtonTextActive: {
    color: '#FFFFFF',
  },
  submitButton: {
    backgroundColor: '#3B82F6',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 16,
  },
  submitButtonDisabled: {
    opacity: 0.6,
  },
  submitButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
});