import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  TextInput,
  Modal,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useData } from '@/contexts/DataContext';
import { useAuth } from '@/contexts/AuthContext';
import { bookingService } from '@/lib/database';
import { ArrowLeft, Star, MapPin, DollarSign, Users, Clock, Calendar, Phone, Plus, Minus, CreditCard } from 'lucide-react-native';

export default function VenueDetails() {
  const { id } = useLocalSearchParams();
  const { venues, addBooking, bookings } = useData();
  const { user } = useAuth();
  const router = useRouter();
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedTime, setSelectedTime] = useState('');
  const [duration, setDuration] = useState(1);
  const [playerNotes, setPlayerNotes] = useState('');
  const [showBookingModal, setShowBookingModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [existingBookings, setExistingBookings] = useState<any[]>([]);

  const venue = venues.find(v => v.id === id);

  useEffect(() => {
    if (venue && selectedDate) {
      fetchExistingBookings();
    }
  }, [venue, selectedDate]);

  const fetchExistingBookings = async () => {
    try {
      const venueBookings = await bookingService.getBookings(undefined, venue!.id);
      const dateBookings = venueBookings.filter(booking =>
        booking.booking_date === selectedDate &&
        (booking.status === 'confirmed' || booking.status === 'pending')
      );
      setExistingBookings(dateBookings);
    } catch (error) {
      console.error('Error fetching bookings:', error);
    }
  };

  if (!venue) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Venue not found</Text>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // Calculate pricing based on time and date
  const calculatePrice = () => {
    if (!selectedDate || !selectedTime) return 0;

    const bookingDate = new Date(selectedDate);
    const isWeekend = bookingDate.getDay() === 0 || bookingDate.getDay() === 6;
    const hour = parseInt(selectedTime.split(':')[0]);
    const nightTimeStart = parseInt(venue.night_time_start.split(':')[0]);
    const isNightTime = hour >= nightTimeStart;

    let pricePerHour = venue.day_price_per_hour;

    if (isWeekend && isNightTime && venue.weekend_night_price) {
      pricePerHour = venue.weekend_night_price;
    } else if (isWeekend && venue.weekend_day_price) {
      pricePerHour = venue.weekend_day_price;
    } else if (isNightTime) {
      pricePerHour = venue.night_price_per_hour;
    }

    return pricePerHour * duration;
  };

  // Check if time slot is available
  const isTimeSlotAvailable = (time: string) => {
    const endTime = `${parseInt(time.split(':')[0]) + duration}:${time.split(':')[1]}`;

    return !existingBookings.some(booking => {
      const bookingStart = booking.start_time;
      const bookingEnd = booking.end_time;

      // Check for overlap
      return (time >= bookingStart && time < bookingEnd) ||
             (endTime > bookingStart && endTime <= bookingEnd) ||
             (time <= bookingStart && endTime >= bookingEnd);
    });
  };

  const handleBooking = () => {
    if (!selectedDate || !selectedTime) {
      Alert.alert('Error', 'Please select a date and time for your booking');
      return;
    }

    if (!user) {
      Alert.alert('Error', 'Please sign in to make a booking');
      return;
    }

    if (!isTimeSlotAvailable(selectedTime)) {
      Alert.alert('Error', 'This time slot is not available. Please select a different time.');
      return;
    }

    setShowBookingModal(true);
  };

  const confirmBooking = async () => {
    try {
      setLoading(true);

      const bookingDate = new Date(selectedDate);
      const isWeekend = bookingDate.getDay() === 0 || bookingDate.getDay() === 6;
      const hour = parseInt(selectedTime.split(':')[0]);
      const nightTimeStart = parseInt(venue.night_time_start.split(':')[0]);
      const isNightTime = hour >= nightTimeStart;
      const endTime = `${parseInt(selectedTime.split(':')[0]) + duration}:${selectedTime.split(':')[1]}`;
      const totalAmount = calculatePrice();

      let pricePerHour = venue.day_price_per_hour;
      if (isWeekend && isNightTime && venue.weekend_night_price) {
        pricePerHour = venue.weekend_night_price;
      } else if (isWeekend && venue.weekend_day_price) {
        pricePerHour = venue.weekend_day_price;
      } else if (isNightTime) {
        pricePerHour = venue.night_price_per_hour;
      }

      await addBooking({
        player_id: user.id,
        venue_id: venue.id,
        booking_date: selectedDate,
        start_time: selectedTime,
        end_time: endTime,
        total_hours: duration,
        price_per_hour: pricePerHour,
        total_amount: totalAmount,
        is_weekend: isWeekend,
        is_night_time: isNightTime,
        status: 'pending',
        payment_status: 'pending',
        player_notes: playerNotes,
      });

      setShowBookingModal(false);
      Alert.alert(
        'Booking Submitted',
        'Your booking request has been submitted. The venue owner will confirm your booking shortly.',
        [{ text: 'OK', onPress: () => router.back() }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to submit booking. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Generate next 7 days for date selection
  const getNextDays = () => {
    const days = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date();
      date.setDate(date.getDate() + i);
      days.push({
        date: date.toISOString().split('T')[0],
        label: date.toLocaleDateString('en-US', { 
          weekday: 'short', 
          month: 'short', 
          day: 'numeric' 
        })
      });
    }
    return days;
  };

  const availableDays = getNextDays();

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: venue.images?.[0] || 'https://images.pexels.com/photos/114296/pexels-photo-114296.jpeg' }}
            style={styles.venueImage}
            resizeMode="cover"
          />
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </View>

        <View style={styles.venueInfo}>
          <View style={styles.venueHeader}>
            <Text style={styles.venueName}>{venue.name}</Text>
            <View style={styles.ratingContainer}>
              <Star size={16} color="#F59E0B" fill="#F59E0B" />
              <Text style={styles.rating}>{venue.rating}</Text>
              <Text style={styles.ratingCount}>({venue.total_bookings} bookings)</Text>
            </View>
          </View>

          <View style={styles.venueLocation}>
            <MapPin size={16} color="#6B7280" />
            <Text style={styles.locationText}>{venue.address}, {venue.city}</Text>
          </View>

          <View style={styles.venueStats}>
            <View style={styles.statItem}>
              <DollarSign size={16} color="#22C55E" />
              <Text style={styles.statText}>${venue.price_per_hour}/hour</Text>
            </View>
            <View style={styles.statItem}>
              <Users size={16} color="#3B82F6" />
              <Text style={styles.statText}>Up to {venue.capacity} people</Text>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Description</Text>
            <Text style={styles.description}>{venue.description}</Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Amenities</Text>
            <View style={styles.amenitiesContainer}>
              {(venue.amenities || []).map((amenity, index) => (
                <View key={index} style={styles.amenityTag}>
                  <Text style={styles.amenityText}>{amenity}</Text>
                </View>
              ))}
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Available Hours</Text>
            <View style={styles.hoursContainer}>
              {(venue.available_hours || []).map((hour, index) => (
                <View key={index} style={styles.hourTag}>
                  <Clock size={14} color="#6B7280" />
                  <Text style={styles.hourText}>{hour}</Text>
                </View>
              ))}
            </View>
          </View>

          {user?.user_type === 'player' && (
            <View style={styles.bookingSection}>
              <Text style={styles.sectionTitle}>Book This Venue</Text>

              <View style={styles.dateSelection}>
                <Text style={styles.selectionLabel}>Select Date:</Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.dateScroll}>
                  {availableDays.map((day) => (
                    <TouchableOpacity
                      key={day.date}
                      style={[
                        styles.dateButton,
                        selectedDate === day.date && styles.dateButtonActive
                      ]}
                      onPress={() => setSelectedDate(day.date)}
                    >
                      <Text style={[
                        styles.dateButtonText,
                        selectedDate === day.date && styles.dateButtonTextActive
                      ]}>
                        {day.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>

              <View style={styles.durationSelection}>
                <Text style={styles.selectionLabel}>Duration:</Text>
                <View style={styles.durationControls}>
                  <TouchableOpacity
                    style={[styles.durationButton, duration <= 1 && styles.durationButtonDisabled]}
                    onPress={() => duration > 1 && setDuration(duration - 1)}
                    disabled={duration <= 1}
                  >
                    <Minus size={16} color={duration <= 1 ? "#D1D5DB" : "#6B7280"} />
                  </TouchableOpacity>
                  <Text style={styles.durationText}>{duration} hour{duration > 1 ? 's' : ''}</Text>
                  <TouchableOpacity
                    style={styles.durationButton}
                    onPress={() => setDuration(duration + 1)}
                  >
                    <Plus size={16} color="#6B7280" />
                  </TouchableOpacity>
                </View>
              </View>

              <View style={styles.timeSelection}>
                <Text style={styles.selectionLabel}>Select Time:</Text>
                <View style={styles.timeGrid}>
                  {(venue.available_hours || []).map((hour) => {
                    const available = selectedDate ? isTimeSlotAvailable(hour) : true;
                    return (
                      <TouchableOpacity
                        key={hour}
                        style={[
                          styles.timeButton,
                          selectedTime === hour && styles.timeButtonActive,
                          !available && styles.timeButtonUnavailable
                        ]}
                        onPress={() => available && setSelectedTime(hour)}
                        disabled={!available}
                      >
                        <Text style={[
                          styles.timeButtonText,
                          selectedTime === hour && styles.timeButtonTextActive,
                          !available && styles.timeButtonTextUnavailable
                        ]}>
                          {hour}
                        </Text>
                      </TouchableOpacity>
                    );
                  })}
                </View>
              </View>

              {selectedDate && selectedTime && (
                <View style={styles.pricingInfo}>
                  <View style={styles.pricingRow}>
                    <Text style={styles.pricingLabel}>Duration:</Text>
                    <Text style={styles.pricingValue}>{duration} hour{duration > 1 ? 's' : ''}</Text>
                  </View>
                  <View style={styles.pricingRow}>
                    <Text style={styles.pricingLabel}>Rate:</Text>
                    <Text style={styles.pricingValue}>${calculatePrice() / duration}/hour</Text>
                  </View>
                  <View style={[styles.pricingRow, styles.totalRow]}>
                    <Text style={styles.totalLabel}>Total:</Text>
                    <Text style={styles.totalValue}>${calculatePrice()}</Text>
                  </View>
                </View>
              )}

              <TouchableOpacity
                style={[styles.bookButton, (!selectedDate || !selectedTime) && styles.bookButtonDisabled]}
                onPress={handleBooking}
                disabled={!selectedDate || !selectedTime}
              >
                <Calendar size={20} color="#FFFFFF" />
                <Text style={styles.bookButtonText}>
                  {selectedDate && selectedTime ? `Book Now - $${calculatePrice()}` : 'Select Date & Time'}
                </Text>
              </TouchableOpacity>
            </View>
          )}

          <View style={styles.contactSection}>
            <Text style={styles.sectionTitle}>Contact Venue</Text>
            <TouchableOpacity style={styles.contactButton}>
              <Phone size={20} color="#3B82F6" />
              <Text style={styles.contactButtonText}>Call Venue Owner</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Booking Confirmation Modal */}
      <Modal
        visible={showBookingModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowBookingModal(false)}>
              <Text style={styles.modalCancelText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Confirm Booking</Text>
            <TouchableOpacity onPress={confirmBooking} disabled={loading}>
              <Text style={[styles.modalConfirmText, loading && { opacity: 0.5 }]}>
                {loading ? 'Booking...' : 'Confirm'}
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.bookingSummary}>
              <Text style={styles.summaryTitle}>Booking Summary</Text>

              <View style={styles.summaryCard}>
                <View style={styles.summaryRow}>
                  <Text style={styles.summaryLabel}>Venue:</Text>
                  <Text style={styles.summaryValue}>{venue.name}</Text>
                </View>
                <View style={styles.summaryRow}>
                  <Text style={styles.summaryLabel}>Date:</Text>
                  <Text style={styles.summaryValue}>
                    {new Date(selectedDate).toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </Text>
                </View>
                <View style={styles.summaryRow}>
                  <Text style={styles.summaryLabel}>Time:</Text>
                  <Text style={styles.summaryValue}>
                    {selectedTime} - {parseInt(selectedTime.split(':')[0]) + duration}:00
                  </Text>
                </View>
                <View style={styles.summaryRow}>
                  <Text style={styles.summaryLabel}>Duration:</Text>
                  <Text style={styles.summaryValue}>{duration} hour{duration > 1 ? 's' : ''}</Text>
                </View>
                <View style={[styles.summaryRow, styles.totalSummaryRow]}>
                  <Text style={styles.summaryTotalLabel}>Total Amount:</Text>
                  <Text style={styles.summaryTotalValue}>${calculatePrice()}</Text>
                </View>
              </View>

              <View style={styles.notesSection}>
                <Text style={styles.notesLabel}>Additional Notes (Optional):</Text>
                <TextInput
                  style={styles.notesInput}
                  value={playerNotes}
                  onChangeText={setPlayerNotes}
                  placeholder="Any special requests or notes for the venue owner..."
                  multiline
                  numberOfLines={3}
                />
              </View>

              <View style={styles.paymentInfo}>
                <View style={styles.paymentHeader}>
                  <CreditCard size={20} color="#3B82F6" />
                  <Text style={styles.paymentTitle}>Payment Information</Text>
                </View>
                <Text style={styles.paymentText}>
                  Payment will be processed after the venue owner confirms your booking.
                  You will receive a notification with payment instructions.
                </Text>
              </View>
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    flex: 1,
  },
  imageContainer: {
    position: 'relative',
  },
  venueImage: {
    width: '100%',
    height: 250,
  },
  backButton: {
    position: 'absolute',
    top: 50,
    left: 20,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonText: {
    color: '#3B82F6',
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  venueInfo: {
    padding: 24,
  },
  venueHeader: {
    marginBottom: 12,
  },
  venueName: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rating: {
    marginLeft: 4,
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  ratingCount: {
    marginLeft: 4,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  venueLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  locationText: {
    marginLeft: 6,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    flex: 1,
  },
  venueStats: {
    flexDirection: 'row',
    gap: 24,
    marginBottom: 24,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    marginLeft: 6,
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#374151',
    lineHeight: 24,
  },
  amenitiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  amenityTag: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  amenityText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#4B5563',
  },
  hoursContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  hourTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EFF6FF',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  hourText: {
    marginLeft: 4,
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#3B82F6',
  },
  bookingSection: {
    backgroundColor: '#F9FAFB',
    padding: 20,
    borderRadius: 12,
    marginBottom: 24,
  },
  dateSelection: {
    marginBottom: 20,
  },
  durationSelection: {
    marginBottom: 20,
  },
  durationControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 16,
  },
  durationButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  durationButtonDisabled: {
    backgroundColor: '#F9FAFB',
    borderColor: '#E5E7EB',
  },
  durationText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    minWidth: 80,
    textAlign: 'center',
  },
  selectionLabel: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 12,
  },
  dateScroll: {
    flexDirection: 'row',
  },
  dateButton: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  dateButtonActive: {
    backgroundColor: '#22C55E',
    borderColor: '#22C55E',
  },
  dateButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  dateButtonTextActive: {
    color: '#FFFFFF',
  },
  timeSelection: {
    marginBottom: 20,
  },
  timeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  timeButton: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  timeButtonActive: {
    backgroundColor: '#22C55E',
    borderColor: '#22C55E',
  },
  timeButtonUnavailable: {
    backgroundColor: '#F3F4F6',
    borderColor: '#E5E7EB',
  },
  timeButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  timeButtonTextActive: {
    color: '#FFFFFF',
  },
  timeButtonTextUnavailable: {
    color: '#D1D5DB',
  },
  pricingInfo: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  pricingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  pricingLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  pricingValue: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#111827',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingTop: 8,
    marginTop: 8,
    marginBottom: 0,
  },
  totalLabel: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  totalValue: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#22C55E',
  },
  bookButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#22C55E',
    paddingVertical: 16,
    borderRadius: 12,
  },
  bookButtonDisabled: {
    backgroundColor: '#D1D5DB',
  },
  bookButtonText: {
    marginLeft: 8,
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  contactSection: {
    marginBottom: 24,
  },
  contactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#3B82F6',
    paddingVertical: 16,
    borderRadius: 12,
  },
  contactButtonText: {
    marginLeft: 8,
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#3B82F6',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorText: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 16,
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  modalCancelText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  modalConfirmText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#22C55E',
  },
  modalContent: {
    flex: 1,
    padding: 24,
  },
  bookingSummary: {
    gap: 20,
  },
  summaryTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 8,
  },
  summaryCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  summaryLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  summaryValue: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#111827',
    flex: 1,
    textAlign: 'right',
  },
  totalSummaryRow: {
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingTop: 12,
    marginTop: 8,
    marginBottom: 0,
  },
  summaryTotalLabel: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  summaryTotalValue: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#22C55E',
  },
  notesSection: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  notesLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginBottom: 8,
  },
  notesInput: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#111827',
    textAlignVertical: 'top',
    minHeight: 80,
  },
  paymentInfo: {
    backgroundColor: '#EFF6FF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#DBEAFE',
  },
  paymentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  paymentTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1E40AF',
    marginLeft: 8,
  },
  paymentText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#1E40AF',
    lineHeight: 20,
  },
});