import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Venue, Booking, Review, User, Strike } from '@/types';
import { venueService, bookingService, reviewService, userService, strikeService } from '@/lib/database';
import { useAuth } from './AuthContext';

interface DataContextType {
  venues: Venue[];
  bookings: Booking[];
  reviews: Review[];
  users: User[];
  strikes: Strike[];
  loading: boolean;
  refreshData: () => Promise<void>;
  addVenue: (venue: Omit<Venue, 'id' | 'created_at' | 'updated_at'>) => Promise<Venue>;
  updateVenue: (id: string, updates: Partial<Venue>) => Promise<Venue>;
  addBooking: (booking: Omit<Booking, 'id' | 'created_at' | 'updated_at'>) => Promise<Booking>;
  updateBooking: (id: string, updates: Partial<Booking>) => Promise<Booking>;
  addReview: (review: Omit<Review, 'id' | 'created_at' | 'updated_at'>) => Promise<Review>;
  updateUser: (id: string, updates: Partial<User>) => Promise<User>;
  approveVenue: (id: string) => Promise<Venue>;
  rejectVenue: (id: string) => Promise<Venue>;
  approveUser: (id: string) => Promise<User>;
  rejectUser: (id: string) => Promise<User>;
  issueStrike: (strike: Omit<Strike, 'id' | 'created_at'>) => Promise<Strike>;
}

const DataContext = createContext<DataContextType | undefined>(undefined);

export function DataProvider({ children }: { children: ReactNode }) {
  const { user } = useAuth();
  const [venues, setVenues] = useState<Venue[]>([]);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [strikes, setStrikes] = useState<Strike[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      refreshData();
    }
  }, [user]);

  // Helper function to transform venue data
  const transformVenueData = (venues: any[]) => {
    return venues.map(venue => {
      const images = venue.venue_images?.map((img: any) => img.image_url) || [];
      // Add fallback image if no images exist
      if (images.length === 0) {
        images.push('https://images.pexels.com/photos/114296/pexels-photo-114296.jpeg');
      }

      return {
        ...venue,
        images,
        amenities: venue.facilities || [],
        available_hours: venue.available_hours || [],
        price_per_hour: venue.day_price_per_hour || 0,
        capacity: venue.capacity || 0,
        total_bookings: venue.total_bookings || 0
      };
    });
  };

  const refreshData = async () => {
    try {
      setLoading(true);

      // Fetch data based on user role
      if (user?.user_type === 'admin') {
        // Admin can see all data
        const [venuesData, bookingsData, usersData, strikesData] = await Promise.all([
          venueService.getVenues(),
          bookingService.getBookings(),
          userService.getUsers(),
          strikeService.getStrikes()
        ]);

        setVenues(transformVenueData(venuesData || []));
        setBookings(bookingsData || []);
        setUsers(usersData || []);
        setStrikes(strikesData || []);
      } else if (user?.user_type === 'venue_owner') {
        // Venue owner sees their venues and related bookings
        const [venuesData, bookingsData] = await Promise.all([
          venueService.getVenues(undefined, user.id),
          bookingService.getBookings(undefined, undefined, undefined)
        ]);

        setVenues(transformVenueData(venuesData || []));
        // Filter bookings for owner's venues
        const ownerVenueIds = venuesData?.map(v => v.id) || [];
        setBookings((bookingsData || []).filter(b => ownerVenueIds.includes(b.venue_id)));
      } else if (user?.user_type === 'player') {
        // Player sees approved venues and their bookings
        const [venuesData, bookingsData] = await Promise.all([
          venueService.getVenues('approved'),
          bookingService.getBookings(user.id)
        ]);

        setVenues(transformVenueData(venuesData || []));
        setBookings(bookingsData || []);
      }

    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const addVenue = async (venueData: Omit<Venue, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const newVenue = await venueService.createVenue(venueData);
      const transformedVenue = transformVenueData([newVenue])[0];
      setVenues(prev => [...prev, transformedVenue]);
      return transformedVenue;
    } catch (error) {
      console.error('Error adding venue:', error);
      throw error;
    }
  };

  const updateVenue = async (id: string, updates: Partial<Venue>) => {
    try {
      const updatedVenue = await venueService.updateVenue(id, updates);
      const transformedVenue = transformVenueData([updatedVenue])[0];
      setVenues(prev => prev.map(venue =>
        venue.id === id ? transformedVenue : venue
      ));
      return transformedVenue;
    } catch (error) {
      console.error('Error updating venue:', error);
      throw error;
    }
  };

  const addBooking = async (bookingData: Omit<Booking, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const newBooking = await bookingService.createBooking(bookingData);
      setBookings(prev => [...prev, newBooking]);
      return newBooking;
    } catch (error) {
      console.error('Error adding booking:', error);
      throw error;
    }
  };

  const updateBooking = async (id: string, updates: Partial<Booking>) => {
    try {
      const updatedBooking = await bookingService.updateBookingStatus(id, updates.status || '', updates.venue_response);
      setBookings(prev => prev.map(booking =>
        booking.id === id ? updatedBooking : booking
      ));
      return updatedBooking;
    } catch (error) {
      console.error('Error updating booking:', error);
      throw error;
    }
  };

  const addReview = async (reviewData: Omit<Review, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const newReview = await reviewService.createReview(reviewData);
      setReviews(prev => [...prev, newReview]);
      // Refresh venues to get updated ratings
      await refreshData();
      return newReview;
    } catch (error) {
      console.error('Error adding review:', error);
      throw error;
    }
  };

  const updateUser = async (id: string, updates: Partial<User>) => {
    try {
      const updatedUser = await userService.updateUser(id, updates);
      setUsers(prev => prev.map(user =>
        user.id === id ? updatedUser : user
      ));
      return updatedUser;
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  };

  const approveVenue = async (id: string) => {
    try {
      if (!user?.id) throw new Error('User not authenticated');
      const updatedVenue = await venueService.approveVenue(id, user.id);
      const transformedVenue = transformVenueData([updatedVenue])[0];
      setVenues(prev => prev.map(venue =>
        venue.id === id ? transformedVenue : venue
      ));
      return transformedVenue;
    } catch (error) {
      console.error('Error approving venue:', error);
      throw error;
    }
  };

  const rejectVenue = async (id: string) => {
    try {
      const updatedVenue = await venueService.rejectVenue(id);
      const transformedVenue = transformVenueData([updatedVenue])[0];
      setVenues(prev => prev.map(venue =>
        venue.id === id ? transformedVenue : venue
      ));
      return transformedVenue;
    } catch (error) {
      console.error('Error rejecting venue:', error);
      throw error;
    }
  };

  const approveUser = async (id: string) => {
    try {
      if (!user?.id) throw new Error('User not authenticated');
      const updatedUser = await userService.approveUser(id, user.id);
      setUsers(prev => prev.map(u =>
        u.id === id ? updatedUser : u
      ));
      return updatedUser;
    } catch (error) {
      console.error('Error approving user:', error);
      throw error;
    }
  };

  const rejectUser = async (id: string) => {
    try {
      const updatedUser = await userService.rejectUser(id);
      setUsers(prev => prev.map(u =>
        u.id === id ? updatedUser : u
      ));
      return updatedUser;
    } catch (error) {
      console.error('Error rejecting user:', error);
      throw error;
    }
  };

  const issueStrike = async (strikeData: Omit<Strike, 'id' | 'created_at'>) => {
    try {
      const newStrike = await strikeService.issueStrike(strikeData);
      setStrikes(prev => [...prev, newStrike]);
      // Refresh data to get updated strike counts
      await refreshData();
      return newStrike;
    } catch (error) {
      console.error('Error issuing strike:', error);
      throw error;
    }
  };

  return (
    <DataContext.Provider value={{
      venues,
      bookings,
      reviews,
      users,
      strikes,
      loading,
      refreshData,
      addVenue,
      updateVenue,
      addBooking,
      updateBooking,
      addReview,
      updateUser,
      approveVenue,
      rejectVenue,
      approveUser,
      rejectUser,
      issueStrike,
    }}>
      {children}
    </DataContext.Provider>
  );
}

export function useData() {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
}