import { supabase, User, Venue, Booking, Review, Strike, VenueImage, TimeSlot } from './supabase';

// User operations
export const userService = {
  async getUsers(userType?: string, status?: string) {
    let query = supabase.from('users').select('*');
    
    if (userType) {
      query = query.eq('user_type', userType);
    }
    
    if (status) {
      query = query.eq('status', status);
    }
    
    const { data, error } = await query.order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  },

  async getUserById(id: string) {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data;
  },

  async updateUser(id: string, updates: Partial<User>) {
    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async approveUser(id: string, approvedBy: string) {
    return this.updateUser(id, {
      status: 'approved',
      updated_at: new Date().toISOString()
    });
  },

  async rejectUser(id: string) {
    return this.updateUser(id, {
      status: 'rejected',
      updated_at: new Date().toISOString()
    });
  },

  async approveUser(id: string, approvedBy: string) {
    return this.updateUser(id, {
      status: 'approved',
      approved_by: approvedBy,
      approved_at: new Date().toISOString()
    });
  },

  async rejectUser(id: string) {
    return this.updateUser(id, { status: 'rejected' });
  },

  async suspendUser(id: string) {
    return this.updateUser(id, { status: 'suspended' });
  }
};

// Venue operations
export const venueService = {
  async getVenues(status?: string, ownerId?: string) {
    let query = supabase
      .from('venues')
      .select(`
        *,
        owner:users!venues_owner_id_fkey(name, email, phone),
        venue_images(*)
      `);
    
    if (status) {
      query = query.eq('status', status);
    }
    
    if (ownerId) {
      query = query.eq('owner_id', ownerId);
    }
    
    const { data, error } = await query.order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  },

  async getVenueById(id: string) {
    const { data, error } = await supabase
      .from('venues')
      .select(`
        *,
        owner:users!venues_owner_id_fkey(name, email, phone),
        venue_images(*),
        reviews!reviews_venue_id_fkey(
          *,
          reviewer:users!reviews_reviewer_id_fkey(name)
        )
      `)
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data;
  },

  async createVenue(venueData: Omit<Venue, 'id' | 'created_at' | 'updated_at'>) {
    // Remove frontend-only fields before inserting
    const { images, amenities, available_hours, price_per_hour, capacity, total_bookings, ...dbVenueData } = venueData as any;

    const { data, error } = await supabase
      .from('venues')
      .insert([dbVenueData])
      .select()
      .single();

    if (error) throw error;

    // Add default image if images were provided
    if (images && images.length > 0) {
      await this.addVenueImage(data.id, images[0], true, 1);
    }

    return data;
  },

  async addVenueImage(venueId: string, imageUrl: string, isPrimary: boolean = false, displayOrder: number = 1) {
    const { data, error } = await supabase
      .from('venue_images')
      .insert([{
        venue_id: venueId,
        image_url: imageUrl,
        is_primary: isPrimary,
        display_order: displayOrder
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async updateVenue(id: string, updates: Partial<Venue>) {
    const { data, error } = await supabase
      .from('venues')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async approveVenue(id: string, approvedBy: string) {
    return this.updateVenue(id, {
      status: 'approved',
      approved_by: approvedBy,
      approved_at: new Date().toISOString()
    });
  },

  async rejectVenue(id: string) {
    return this.updateVenue(id, { status: 'rejected' });
  },

  async searchVenues(filters: {
    city?: string;
    priceRange?: [number, number];
    rating?: number;
    date?: string;
  }) {
    let query = supabase
      .from('venues')
      .select(`
        *,
        venue_images(*)
      `)
      .eq('status', 'approved');
    
    if (filters.city) {
      query = query.ilike('city', `%${filters.city}%`);
    }
    
    if (filters.priceRange) {
      query = query
        .gte('day_price_per_hour', filters.priceRange[0])
        .lte('day_price_per_hour', filters.priceRange[1]);
    }
    
    if (filters.rating) {
      query = query.gte('rating', filters.rating);
    }
    
    const { data, error } = await query.order('rating', { ascending: false });
    
    if (error) throw error;
    return data;
  }
};

// Booking operations
export const bookingService = {
  async getBookings(playerId?: string, venueId?: string, status?: string) {
    let query = supabase
      .from('bookings')
      .select(`
        *,
        player:users!bookings_player_id_fkey(name, email, phone),
        venue:venues!bookings_venue_id_fkey(name, address, phone)
      `);
    
    if (playerId) {
      query = query.eq('player_id', playerId);
    }
    
    if (venueId) {
      query = query.eq('venue_id', venueId);
    }
    
    if (status) {
      query = query.eq('status', status);
    }
    
    const { data, error } = await query.order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  },

  async createBooking(bookingData: Omit<Booking, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('bookings')
      .insert([bookingData])
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async updateBookingStatus(id: string, status: string, response?: string) {
    const updates: any = { status };
    
    if (status === 'confirmed') {
      updates.confirmed_at = new Date().toISOString();
    }
    
    if (status === 'cancelled') {
      updates.cancelled_at = new Date().toISOString();
    }
    
    if (response) {
      updates.venue_response = response;
    }
    
    const { data, error } = await supabase
      .from('bookings')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }
};

// Review operations
export const reviewService = {
  async createReview(reviewData: Omit<Review, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('reviews')
      .insert([reviewData])
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async getReviewsForPlayer(playerId: string) {
    const { data, error } = await supabase
      .from('reviews')
      .select(`
        *,
        venue:venues(name)
      `)
      .eq('reviewee_id', playerId)
      .eq('review_type', 'venue_to_player')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  },

  async getReviewsForVenue(venueId: string) {
    const { data, error } = await supabase
      .from('reviews')
      .select(`
        *,
        player:users!reviews_reviewer_id_fkey(name)
      `)
      .eq('venue_id', venueId)
      .eq('review_type', 'player_to_venue')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  },

  async getReviewsForVenue(venueId: string) {
    const { data, error } = await supabase
      .from('reviews')
      .select(`
        *,
        reviewer:users!reviews_reviewer_id_fkey(name)
      `)
      .eq('venue_id', venueId)
      .eq('review_type', 'player_to_venue')
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  },

  async getReviewsForPlayer(playerId: string) {
    const { data, error } = await supabase
      .from('reviews')
      .select(`
        *,
        reviewer:users!reviews_reviewer_id_fkey(name),
        venue:venues!reviews_venue_id_fkey(name)
      `)
      .eq('reviewee_id', playerId)
      .eq('review_type', 'venue_to_player')
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  }
};

// Strike operations
export const strikeService = {
  async issueStrike(strikeData: Omit<Strike, 'id' | 'created_at'>) {
    const { data, error } = await supabase
      .from('strikes')
      .insert([strikeData])
      .select()
      .single();
    
    if (error) throw error;
    
    // Update strike count for user or venue
    if (strikeData.user_id) {
      await supabase.rpc('increment_user_strikes', { user_id: strikeData.user_id });
    }
    
    if (strikeData.venue_id) {
      await supabase.rpc('increment_venue_strikes', { venue_id: strikeData.venue_id });
    }
    
    return data;
  },

  async getStrikes(userId?: string, venueId?: string) {
    let query = supabase
      .from('strikes')
      .select(`
        *,
        issued_by_user:users!strikes_issued_by_fkey(name)
      `);
    
    if (userId) {
      query = query.eq('user_id', userId);
    }
    
    if (venueId) {
      query = query.eq('venue_id', venueId);
    }
    
    const { data, error } = await query.order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  }
};
